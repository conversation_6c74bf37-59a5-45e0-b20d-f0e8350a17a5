<template>
  <div>
    <!-- 搜索表单 -->
    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form
          ref="queryForm"
          :model="queryForm"
          label-width="100px"
          label-position="right"
          @submit.native.prevent
          inline
        >
          <div class="acea-row search-form">
            <div class="search-form-box">
              <el-form-item label="用户ID：">
                <el-input v-model="queryForm.id" placeholder="请输入用户ID" clearable class="form_content_width" />
              </el-form-item>
              <el-form-item label="用户名：">
                <el-input v-model="queryForm.username" placeholder="请输入用户名" clearable class="form_content_width" />
              </el-form-item>
              <el-form-item label="昵称：">
                <el-input v-model="queryForm.nickname" placeholder="请输入昵称" clearable class="form_content_width" />
              </el-form-item>
            </div>
          </div>
          <div class="acea-row search-form">
            <div class="search-form-box">
              <el-form-item label="手机号：">
                <el-input v-model="queryForm.mobile" placeholder="请输入手机号" clearable class="form_content_width" />
              </el-form-item>
              <el-form-item label="性别：">
                <el-select v-model="queryForm.gender" placeholder="请选择性别" clearable class="form_content_width">
                  <el-option value="" label="全部"></el-option>
                  <el-option :value="1" label="男"></el-option>
                  <el-option :value="2" label="女"></el-option>
                  <el-option :value="0" label="未知"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="推广等级：">
                <el-select v-model="queryForm.promotionLevel" placeholder="请选择推广等级" clearable class="form_content_width">
                  <el-option value="" label="全部"></el-option>
                  <el-option :value="0" label="普通用户"></el-option>
                  <el-option :value="1" label="推广员"></el-option>
                  <el-option :value="2" label="高级推广员"></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="acea-row search-form">
            <div class="search-form-box">
              <el-form-item label="注册时间：">
                <el-date-picker
                  v-model="registerTimeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  class="form_content_width"
                />
              </el-form-item>
              <el-form-item label="余额范围：">
                <el-input v-model="queryForm.minBalance" placeholder="最小余额" clearable style="width: 120px" />
                <span style="margin: 0 10px">-</span>
                <el-input v-model="queryForm.maxBalance" placeholder="最大余额" clearable style="width: 120px" />
              </el-form-item>
            </div>
          </div>
          <div class="acea-row search-form">
            <div class="search-form-box">
              <el-form-item label="积分范围：">
                <el-input v-model="queryForm.minPoints" placeholder="最小积分" clearable style="width: 120px" />
                <span style="margin: 0 10px">-</span>
                <el-input v-model="queryForm.maxPoints" placeholder="最大积分" clearable style="width: 120px" />
              </el-form-item>
              <el-form-item class="search-form-sub">
                <el-button type="primary" v-db-click @click="handleSearch">搜索</el-button>
                <el-button class="ResetSearch" v-db-click @click="handleReset">重置</el-button>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card :bordered="false" shadow="never" class="mt16">
      <el-table
        :data="userList"
        ref="table"
        class="mt14"
        v-loading="loading"
        highlight-current-row
        empty-text="暂无数据"
      >
        <el-table-column label="用户ID" width="80" prop="id" sortable />
        <el-table-column label="头像" width="80">
          <template slot-scope="scope">
            <div class="tabBox_img" v-viewer>
              <img v-lazy="scope.row.avatar || '/static/images/default-avatar.png'" style="width: 40px; height: 40px; border-radius: 50%" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户名" min-width="120" prop="username" />
        <el-table-column label="昵称" min-width="120" prop="nickname" />
        <el-table-column label="手机号" min-width="120" prop="mobile" />
        <el-table-column label="性别" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.gender === 1" style="color: #2db7f5">男</span>
            <span v-else-if="scope.row.gender === 2" style="color: #ed4014">女</span>
            <span v-else>未知</span>
          </template>
        </el-table-column>
        <el-table-column label="用户等级" width="100" prop="userLevelId" />
        <el-table-column label="余额" width="100" prop="balance" sortable>
          <template slot-scope="scope">
            <span>{{ scope.row.balance || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="积分" width="100" prop="points" sortable>
          <template slot-scope="scope">
            <span>{{ scope.row.points || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="推广等级" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.promotionLevel === 0" type="info">普通用户</el-tag>
            <el-tag v-else-if="scope.row.promotionLevel === 1" type="success">推广员</el-tag>
            <el-tag v-else-if="scope.row.promotionLevel === 2" type="warning">高级推广员</el-tag>
            <el-tag v-else type="info">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="推广人数" width="100" prop="promotionCount" sortable>
          <template slot-scope="scope">
            <span>{{ scope.row.promotionCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" min-width="160" prop="registerTime" sortable>
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.registerTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最后登录" min-width="160" prop="lastLoginTime" sortable>
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.lastLoginTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewUserDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="acea-row row-right page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryForm.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryForm.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>

    <!-- 用户详情弹窗 -->
    <el-dialog title="用户详情" :visible.sync="detailVisible" width="800px">
      <div v-if="currentUser" class="user-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>用户ID：</label>
              <span>{{ currentUser.id }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>用户名：</label>
              <span>{{ currentUser.username }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>昵称：</label>
              <span>{{ currentUser.nickname }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>手机号：</label>
              <span>{{ currentUser.mobile }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>性别：</label>
              <span>{{ getGenderText(currentUser.gender) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>生日：</label>
              <span>{{ formatDate(currentUser.birthday) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>用户等级：</label>
              <span>{{ currentUser.userLevelId }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>余额：</label>
              <span>{{ currentUser.balance || 0 }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>积分：</label>
              <span>{{ currentUser.points || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>推广等级：</label>
              <span>{{ getPromotionLevelText(currentUser.promotionLevel) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>推广人数：</label>
              <span>{{ currentUser.promotionCount || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>推广码：</label>
              <span>{{ currentUser.promotionCode }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>注册时间：</label>
              <span>{{ formatDate(currentUser.registerTime) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>最后登录：</label>
              <span>{{ formatDate(currentUser.lastLoginTime) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>注册IP：</label>
              <span>{{ currentUser.registerIp }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>最后登录IP：</label>
              <span>{{ currentUser.lastLoginIp }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <label>微信OpenID：</label>
              <span>{{ currentUser.wechatOpenId }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { userQueryList } from '@/api/user';
import dayjs from 'dayjs';

export default {
  name: 'UserQuery',
  data() {
    return {
      loading: false,
      userList: [],
      total: 0,
      queryForm: {
        page: 1,
        limit: 20,
        id: '',
        username: '',
        nickname: '',
        mobile: '',
        gender: '',
        promotionLevel: '',
        minBalance: '',
        maxBalance: '',
        minPoints: '',
        maxPoints: '',
        registerTimeStart: '',
        registerTimeEnd: ''
      },
      registerTimeRange: [],
      detailVisible: false,
      currentUser: null
    };
  },
  mounted() {
    this.getUserList();
  },
  methods: {
    // 获取用户列表
    getUserList() {
      this.loading = true;
      
      // 处理时间范围
      if (this.registerTimeRange && this.registerTimeRange.length === 2) {
        this.queryForm.registerTimeStart = this.registerTimeRange[0];
        this.queryForm.registerTimeEnd = this.registerTimeRange[1];
      } else {
        this.queryForm.registerTimeStart = '';
        this.queryForm.registerTimeEnd = '';
      }

      userQueryList(this.queryForm)
        .then((res) => {
          this.userList = res.data.list || [];
          this.total = res.data.count || 0;
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          this.$message.error(err.msg || '获取用户列表失败');
        });
    },
    
    // 搜索
    handleSearch() {
      this.queryForm.page = 1;
      this.getUserList();
    },
    
    // 重置
    handleReset() {
      this.queryForm = {
        page: 1,
        limit: 20,
        id: '',
        username: '',
        nickname: '',
        mobile: '',
        gender: '',
        promotionLevel: '',
        minBalance: '',
        maxBalance: '',
        minPoints: '',
        maxPoints: '',
        registerTimeStart: '',
        registerTimeEnd: ''
      };
      this.registerTimeRange = [];
      this.getUserList();
    },
    
    // 分页大小改变
    handleSizeChange(size) {
      this.queryForm.limit = size;
      this.queryForm.page = 1;
      this.getUserList();
    },
    
    // 当前页改变
    handleCurrentChange(page) {
      this.queryForm.page = page;
      this.getUserList();
    },
    
    // 查看用户详情
    viewUserDetail(user) {
      this.currentUser = user;
      this.detailVisible = true;
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return '-';
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    },
    
    // 获取性别文本
    getGenderText(gender) {
      switch (gender) {
        case 1: return '男';
        case 2: return '女';
        default: return '未知';
      }
    },
    
    // 获取推广等级文本
    getPromotionLevelText(level) {
      switch (level) {
        case 0: return '普通用户';
        case 1: return '推广员';
        case 2: return '高级推广员';
        default: return '未设置';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.search-form {
  margin-bottom: 10px;
}

.search-form-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
}

.form_content_width {
  width: 200px;
}

.tabBox_img {
  display: flex;
  justify-content: center;
  align-items: center;
}

.page {
  margin-top: 20px;
  padding: 20px 0;
}

.user-detail {
  .detail-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    
    label {
      font-weight: bold;
      min-width: 100px;
      color: #606266;
    }
    
    span {
      color: #303133;
    }
  }
}

.mt16 {
  margin-top: 16px;
}

.padding-add {
  padding: 20px;
}
</style>
